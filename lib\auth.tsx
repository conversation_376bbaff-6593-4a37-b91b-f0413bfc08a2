import Credentials<PERSON>rovider from "next-auth/providers/credentials";

export const authOptions = {
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        const res = await fetch("http://localhost:3001/api/auth/login", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(credentials),
        });

        const data = await res.json();

        if (!res.ok) {
            console.error("Authorization failed:", data.message); // Log error
            return null; // Return null instead of throwing an error
        }

        return {
            id: data.id,
            email: data.email,
            accessToken: data.token,
            accessTokenExpires: Date.now() + 4 * 60 * 60 * 1000,
        };
    }



    }),
  ],
  callbacks: {
    async jwt({ token, user }:any) {
      if (user) {
        token.accessToken = user.accessToken;
        token.accessTokenExpires = user.accessTokenExpires;
      }

      // Refresh token if expired
      if (Date.now() > token.accessTokenExpires) {
        try {
          const res = await fetch("http://localhost:3001/api/auth/refresh", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ token: token.accessToken }),
          });

          const refreshedToken = await res.json();
          if (!res.ok) throw new Error("Failed to refresh token");

          return {
            ...token,
            accessToken: refreshedToken.token,
            accessTokenExpires: Date.now() + 4 * 60 * 60 * 1000,
          };
        } catch (error) {
          return { ...token, error: "RefreshAccessTokenError" };
        }
      }
      return token;
    },
    async session({ session, token }:any) {
      session.user = { id: token.id, email: token.email };
      session.accessToken = token.accessToken;
      session.error = token.error;
      return session;
    },
  },
  secret: process.env.NEXTAUTH_SECRET,
  pages: {
    signIn: "/login", // Custom login page
    error: "/login",  // Redirect all errors to the login page
  },

};
