'use client';
import React, { useEffect, useState } from 'react';
import { DateFilter } from './DateFilter';
import API_ENDPOINTS from '@/app/lib/apiRoutes';
import Loading from '@/components/layouts/loading';

import { useRouter } from 'next/navigation';
import DashboardHeader from '@/components/pages/dashboard/components/DashboardHeader';
import ApplicationStatsGrid from '@/components/pages/dashboard/components/ApplicationStatsGrid';
import ActiveUsersCard from '@/components/pages/dashboard/components/ActiveUsersCard';
import QuickActionsCard from '@/components/pages/dashboard/components/QuickActionsCard';
import RevenueStatsGrid from '@/components/pages/dashboard/components/RevenueStatsGrid';
import { useSelector, useDispatch } from 'react-redux';
import { IRootState } from '@/store';
import { fetchUserProfile } from '@/store/utils.ts/userActions';

const Application = () => {
    // Get user details from Redux store
    const { userDetails, userSession } = useSelector((state: IRootState) => state.user);
    const dispatch = useDispatch();
    const { push } = useRouter();

    const [selectedStatus, setSelectedStatus] = useState('All');
    const [loader, setLoader] = useState(false);
    const [date, setDate] = useState({
        startDate: '',
        endDate: '',
    });
    const [data, setData] = useState({
        agentApplications: 0,
        agencyApplications: 0,
        advertisements: 0,
    });

    // Additional state for new dashboard components
    const [activeUsersStats, setActiveUsersStats] = useState({
        activeAgents: 0,
        activeAgencies: 0,
        activeWebUsers: 0,
    });

    const [revenueStats, setRevenueStats] = useState({
        subscriptionRevenue: 0,
        adsRevenue: 0,
    });

    const [revenueGrowth, setRevenueGrowth] = useState({
        subscriptions: 0,
        ads: 0,
    });

    // Fetch user profile on component mount
    useEffect(() => {
        if (!userSession && !userDetails) {
            dispatch(fetchUserProfile(() => push('/login')) as any);
        }
    }, [dispatch, userSession, userDetails, push]);

    // Get user's full name and role
    interface UserDetails {
        firstName?: string;
        lastName?: string;
        name?: string;
        email?: string;
        role?: string;
        designation?: string;
        userType?: string;
    }

    const getUserFullName = () => {
        const user = userDetails as UserDetails;

        // Debug log to check what data we have
        console.log('User details:', user);

        if (user?.firstName && user?.lastName) {
            return `${user.firstName} ${user.lastName}`;
        } else if (user?.firstName) {
            return user.firstName;
        } else if (user?.name) {
            return user.name;
        } else if (user?.email) {
            return user.email;
        }

        // Fallback: try to get from localStorage if available
        if (typeof window !== 'undefined') {
            const storedUser = localStorage.getItem('userDetails');
            if (storedUser) {
                try {
                    const parsedUser = JSON.parse(storedUser);
                    if (parsedUser?.firstName && parsedUser?.lastName) {
                        return `${parsedUser.firstName} ${parsedUser.lastName}`;
                    } else if (parsedUser?.name) {
                        return parsedUser.name;
                    }
                } catch (e) {
                    console.error('Error parsing stored user details:', e);
                }
            }
        }

        return 'Admin User';
    };

    const getUserRole = () => {
        const user = userDetails as any;

        // Debug log to check what data we have
        console.log('User role data:', user?.role, user?.designation, user?.userType);

        const role = user?.role || user?.designation || user?.userType;

        if (role) {
            return role;
        }

        // Fallback: try to get from localStorage if available
        if (typeof window !== 'undefined') {
            const storedUser = localStorage.getItem('userDetails');
            if (storedUser) {
                try {
                    const parsedUser = JSON.parse(storedUser);
                    return parsedUser?.role || parsedUser?.designation || parsedUser?.userType;
                } catch (e) {
                    console.error('Error parsing stored user details:', e);
                }
            }
        }

        return 'Super Admin';
    };

    const handleDateChange = (startDate: string, endDate: string) => {
        setDate({
            startDate: startDate,
            endDate: endDate,
        });
    };

    const fetchData = async () => {
        try {
            const myHeaders = new Headers();
            setLoader(true);
            const requestOptions: RequestInit = {
                method: 'GET',
                headers: myHeaders,
                redirect: 'follow',
                credentials: 'include',
            };
            await fetch(API_ENDPOINTS.APPLICATION_COUNT + `?startDate=${date.startDate}&endDate=${date.endDate}&status=${selectedStatus}`, requestOptions)
                .then((response) => response.json())
                .then((result) => {
                    // Parse the new API response structure
                    const dashboardData = result.data;

                    // Set data for applications (using active counts as proxy)
                    const formattedData = {
                        agentApplications: dashboardData.activeCounts?.activeAgents || 0,
                        agencyApplications: dashboardData.activeCounts?.activeAgencies || 0,
                        advertisements: 0, // Not provided in new response
                    };

                    setData(formattedData);

                    // Set active users stats from new response
                    setActiveUsersStats({
                        activeAgents: dashboardData.activeCounts?.activeAgents || 0,
                        activeAgencies: dashboardData.activeCounts?.activeAgencies || 0,
                        activeWebUsers: dashboardData.webUserMetrics?.totalWebUsers || 0,
                    });

                    // Set revenue stats from new response
                    setRevenueStats({
                        subscriptionRevenue: dashboardData.subscriptionMetrics?.totalRevenue || 0,
                        adsRevenue: 0, // Not provided in new response
                    });

                    // Set revenue growth (can be calculated or set to 0)
                    setRevenueGrowth({
                        subscriptions: 0, // Not provided, set to 0
                        ads: 0, // Not provided, set to 0
                    });

                    setLoader(false);
                })
                .catch((error) => {
                    console.error(error);
                    setLoader(false);
                });
        } catch (error) {
            console.error(error);
            setLoader(false);
        }
    };

    // Helper functions for the new dashboard components
    const getFilteredCount = () => {
        if (selectedStatus === 'All') {
            return data.agentApplications + data.agencyApplications;
        }
        // For specific status, return a calculated value based on the filter
        return Math.floor((data.agentApplications + data.agencyApplications) * 0.6);
    };

    const getFilterLabel = () => {
        return selectedStatus === 'All' ? 'Total Applications' : `${selectedStatus} Applications`;
    };

    const handleCardClick = (cardType: string, role?: string) => {
        if (cardType === 'agent' || cardType === 'agency') {
            push(`/dashboard/agents?status=${selectedStatus}&startDate=${date.startDate}&endDate=${date.endDate}&role=${role}`);
        }
        // Handle other card clicks as needed
    };

    const handleActionClick = (action: string) => {
        switch (action) {
            case 'support-tickets':
                push('/support-tickets');
                break;
            case 'lead-management':
                push('/dashboard/lead-management');
                break;
            case 'manage-users':
                push('/user-management');
                break;
            case 'reviews-management':
                push('/reviews-management');
                break;
            default:
                break;
        }
    };

    // Removed automatic navigation on click per UX request: clicks should no longer redirect.
    const handleUsersCardClick = (cardType: string) => {
        // no-op: intentionally left blank to prevent redirection
        return;
    };

    const handleRevenueCardClick = (cardType: string) => {
        // no-op: intentionally left blank to prevent redirection
        return;
    };

    // useEffect(() => {
    //     fetchData();
    // }, []);

    useEffect(() => {
        fetchData();
    }, [date, selectedStatus]);
    return (
        <>
            {loader && <Loading />}
            <div className="w-full gap-4">
                {/* Welcome Section */}

                <div className="flex flex-col gap-4 rounded-lg border p-5 pb-7 pt-5 font-inter">
                    {/* Enhanced Header with new dashboard header */}
                    <DashboardHeader applicationFilter={selectedStatus} onApplicationFilterChange={setSelectedStatus} />
                    {/* <div className="mb-6 rounded-lg bg-[#eff4ff] p-6">
                        <h1 className="mb-2 text-2xl font-bold text-gray-900">Welcome {getUserFullName()}!</h1>
                        <p className="text-gray-600">
                            You're logged in as <span className="font-medium text-blue-600">{getUserRole()}</span>
                        </p>
                    </div> */}
                    {/* Enhanced Application Stats Grid */}
                    <ApplicationStatsGrid stats={data} applicationFilter={selectedStatus} onCardClick={handleCardClick} getFilteredCount={getFilteredCount} getFilterLabel={getFilterLabel} />

                    {/* Revenue Stats Grid - Moved to appear after Application Stats */}
                    <RevenueStatsGrid stats={revenueStats} revenueGrowth={revenueGrowth} onCardClick={handleRevenueCardClick} />

                    {/* Additional Dashboard Components */}
                    <ActiveUsersCard stats={activeUsersStats} onCardClick={handleUsersCardClick} />

                    <QuickActionsCard onActionClick={handleActionClick} />
                </div>
            </div>
        </>
    );
};

export default Application;
