import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { Property } from '@/utils/types/property';

interface AddNoteToPropertyDialogProps {
  property: Property;
  children?: React.ReactNode;
  onAddNote?: (propertyId: number, note: string) => void;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

const AddNoteToPropertyDialog: React.FC<AddNoteToPropertyDialogProps> = ({
  property,
  children,
  onAddNote,
  open,
  onOpenChange
}) => {
  const [note, setNote] = useState('');
  const [notes, setNotes] = useState<any[]>([]);
  const { toast } = useToast();
  // lazy-load notes when modal opens
  useEffect(() => {
    if (open && property?.id) {
      (async () => {
        try {
          // dynamic import to avoid circular dependencies
          const propertyService = (await import('@/lib/propertyService')).default;
          const res = await propertyService.getNotes(property.id);
          if (Array.isArray(res)) {
            setNotes(res);
          } else if (res && Array.isArray(res.data)) {
            setNotes(res.data);
          } else if (res && Array.isArray((res as any).notes)) {
            setNotes((res as any).notes);
          } else {
            setNotes([]);
          }
        } catch (err) {
          // eslint-disable-next-line no-console
          console.error('Failed to load notes:', err);
          setNotes([]);
        }
      })();
    }
  }, [open, property?.id]);

  const handleSubmit = () => {
    if (!note.trim()) return;

    // Call the callback if provided
    if (onAddNote) {
      onAddNote(property.id, note);
    }

    // Refresh notes list after adding
    (async () => {
      try {
        const propertyService = (await import('@/lib/propertyService')).default;
        const res = await propertyService.getNotes(property.id);
        if (Array.isArray(res)) setNotes(res);
        else if (res && Array.isArray(res.data)) setNotes(res.data);
      } catch (e) {
        /* ignore */
      }
    })();

    toast({
      title: "Note added",
      description: "Note has been successfully added to the property.",
    });
    setNote('');

    // Close the modal
    if (onOpenChange) {
      onOpenChange(false);
    }
  };



  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {children && (
        <DialogTrigger asChild>
          {children}
        </DialogTrigger>
      )}
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>Add Note</DialogTitle>
          <DialogDescription>
            Add a note to property <strong>{property.title || property.name || 'Unknown Property'}</strong>
            {property.agentName && ` by ${property.agencyName}`}
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 p-6">
          {/* Existing notes */}
          <div className="mb-4">
            <h4 className="text-sm font-semibold mb-2">Notes ({notes.length})</h4>
            <div className="max-h-40 overflow-y-auto space-y-2">
              {notes.length === 0 ? (
                <div className="text-sm text-gray-500">No notes yet</div>
              ) : (
                notes.map((n, idx) => (
                  <div key={n.id || idx} className="rounded-md bg-gray-50 p-2">
                    <div className="text-xs text-gray-600">{n.createdAt || n.created_at || n.created || 'Unknown date'}</div>
                    <div className="text-sm text-gray-800">{n.note || n.notes || n.message}</div>
                  </div>
                ))
              )}
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="note">Note</Label>
            <Textarea
              id="note"
              value={note}
              onChange={(e) => setNote(e.target.value)}
              placeholder="Enter your note here..."
              rows={4}
              className="min-h-[100px] resize-none"
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange?.(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit}>
            Add Note
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AddNoteToPropertyDialog;
