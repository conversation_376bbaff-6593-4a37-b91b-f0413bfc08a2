import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { DollarSign, Target, TrendingUp } from 'lucide-react';

interface RevenueStats {
  subscriptionRevenue: number;
  adsRevenue: number;
}

interface RevenueGrowth {
  subscriptions: number;
  ads: number;
}

interface RevenueStatsGridProps {
  stats: RevenueStats;
  revenueGrowth: RevenueGrowth;
  onCardClick: (cardType: string) => void;
}

const RevenueStatsGrid: React.FC<RevenueStatsGridProps> = ({
  stats,
  revenueGrowth,
  onCardClick
}) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
      <Card
        className="hover:shadow-lg transition-shadow"
      >
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold font-inter">Subscription Revenue</CardTitle>
            <DollarSign className="w-6 h-6 text-green-600" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold text-gray-900 mb-2">
            AED {stats.subscriptionRevenue.toLocaleString()}
          </div>
          <div className="flex items-center text-sm">
            <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
            <span className="text-green-600 font-medium">+{revenueGrowth.subscriptions}%</span>
            <span className="text-gray-500 ml-1">from last month</span>
          </div>
        </CardContent>
      </Card>

      <Card
        className="hover:shadow-lg transition-shadow"
      >
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold font-inter">Ads Revenue</CardTitle>
            <Target className="w-6 h-6 text-orange-600" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold text-gray-900 mb-2">
            AED {stats.adsRevenue.toLocaleString()}
          </div>
          <div className="flex items-center text-sm">
            <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
            <span className="text-green-600 font-medium">+{revenueGrowth.ads}%</span>
            <span className="text-gray-500 ml-1">from last month</span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RevenueStatsGrid;
