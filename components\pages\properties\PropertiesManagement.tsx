import React, { useState, useEffect, useCallback } from 'react';
import { Property, PropertyStats, PropertiesResponse, PropertyPagination, PropertyStatusCount } from '@/utils/types/property';
import propertyService from '@/lib/propertyService';
import PropertyHeader from './PropertyHeader';
import PropertyFilters from './PropertyFilters';
import PropertyTable from './PropertyTable';
import { useToast } from '@/components/reusable/Notify';
import { showMessage } from '@/app/lib/Alert';
import { PROPERTIES_API } from '@/app/lib/apiRoutes';

// Debounce utility function
function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  const debounced = (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
  debounced.cancel = () => clearTimeout(timeoutId);
  return debounced;
}

const PropertiesManagement: React.FC = () => {
  const [properties, setProperties] = useState<Property[]>([]);
  const [selectedProperties, setSelectedProperties] = useState<string[]>([]);
  const [pagination, setPagination] = useState<PropertyPagination | null>(null);
  const [statusCounts, setStatusCounts] = useState<PropertyStatusCount[]>([]);
  const [loading, setLoading] = useState(false);

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('All Types');
  const [statusFilter, setStatusFilter] = useState('All Status');
  const [locationFilter, setLocationFilter] = useState('All Emirates');
  const [listingTypeFilter, setListingTypeFilter] = useState('All Types');

  // Filter options states
  const [propertyTypes, setPropertyTypes] = useState<Array<{ id: number; name: string }>>([]);
  const [locations, setLocations] = useState<Array<{ id: number; name: string }>>([]);
  const [agents, setAgents] = useState<Array<{ id: number; name: string }>>([]);
  const [statuses, setStatuses] = useState<Array<{ id: number; name: string }>>([]);
  const [listingTypes, setListingTypes] = useState<Array<{ id: number; name: string }>>([]);

  // Sorting states
  const [sortBy, setSortBy] = useState('title');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');


  // Debounced search function
  const debouncedSearch = useCallback(
    debounce(async (searchValue: string) => {
      setLoading(true);
      try {

        // Convert filter values to API parameters
        const statusParam = statusFilter === 'All Status' ? '' : statusFilter;
        const propertyTypeIdParam = typeFilter === 'All Types' ? '' :
          propertyTypes.find(type => type.name === typeFilter)?.id?.toString() || '';
        const locationIdParam = locationFilter === 'All Emirates' ? '' :
          locations.find(location => location.name === locationFilter)?.id?.toString() || '';
        const listingTypeParam = listingTypeFilter === 'All Types' ? '' :
          listingTypes.find(type => type.name === listingTypeFilter)?.id?.toString() ||
          (listingTypeFilter === 'Rent' ? '43' : listingTypeFilter === 'Sale' ? '44' : ''); // Fallback for Rent/Sale

        const response = await propertyService.getProperties(
          1, // page
          10, // pageSize
          statusParam,
          propertyTypeIdParam,
          locationIdParam,
          listingTypeParam,
          searchValue // Use the debounced search value
        );

        console.log('API Response:', response);
        if (response.success) {
          console.log('Properties received:', response.data.properties);
          setProperties(response.data.properties);
          setPagination(response.data.pagination);
          if (response.data.statusCounts) {
            setStatusCounts(response.data.statusCounts);
            console.log('Status counts received:', response.data.statusCounts);
          }
        }
      } catch (error) {
        console.error('Error fetching properties:', error);
        // For now, keep mock data as fallback
      } finally {
        setLoading(false);
      }
    }, 500), // 500ms debounce delay
    [typeFilter, statusFilter, locationFilter, listingTypeFilter, propertyTypes, locations, listingTypes]
  );

  useEffect(() => {
    const fetchProperties = async () => {
      setLoading(true);
      try {

        // Convert filter values to API parameters
        const statusParam = statusFilter === 'All Status' ? '' : statusFilter;
        const propertyTypeIdParam = typeFilter === 'All Types' ? '' :
          propertyTypes.find(type => type.name === typeFilter)?.id?.toString() || '';
        const locationIdParam = locationFilter === 'All Emirates' ? '' :
          locations.find(location => location.name === locationFilter)?.id?.toString() || '';
        const listingTypeParam = listingTypeFilter === 'All Types' ? '' :
          listingTypes.find(type => type.name === listingTypeFilter)?.id?.toString() ||
          (listingTypeFilter === 'Rent' ? '43' : listingTypeFilter === 'Sale' ? '44' : ''); // Fallback for Rent/Sale

        const response = await propertyService.getProperties(
          1, // page
          10, // pageSize
          statusParam,
          propertyTypeIdParam,
          locationIdParam,
          listingTypeParam,
          '' // No search for initial load
        );

        console.log('API Response:', response);
        if (response.success) {
          console.log('Properties received:', response.data.properties);
          setProperties(response.data.properties);
          setPagination(response.data.pagination);
          if (response.data.statusCounts) {
            setStatusCounts(response.data.statusCounts);
            console.log('Status counts received:', response.data.statusCounts);
          }
        }
      } catch (error) {
        console.error('Error fetching properties:', error);
        // For now, keep mock data as fallback
      } finally {
        setLoading(false);
      }
    };

    fetchProperties();
  }, [typeFilter, statusFilter, locationFilter, listingTypeFilter, propertyTypes, locations, listingTypes]);

  // Effect for debounced search
  useEffect(() => {
    // Cancel any pending debounced search
    (debouncedSearch as any).cancel?.();

    if (searchTerm !== '') {
      debouncedSearch(searchTerm);
    } else {
      // When search is cleared, fetch all properties without search
      const fetchAllProperties = async () => {
        setLoading(true);
        try {

          // Convert filter values to API parameters
          const statusParam = statusFilter === 'All Status' ? '' : statusFilter;
          const propertyTypeIdParam = typeFilter === 'All Types' ? '' :
            propertyTypes.find(type => type.name === typeFilter)?.id?.toString() || '';
          const locationIdParam = locationFilter === 'All Emirates' ? '' :
            locations.find(location => location.name === locationFilter)?.id?.toString() || '';
          const listingTypeParam = listingTypeFilter === 'All Types' ? '' :
            listingTypes.find(type => type.name === listingTypeFilter)?.id?.toString() ||
            (listingTypeFilter === 'Rent' ? '43' : listingTypeFilter === 'Sale' ? '44' : ''); // Fallback for Rent/Sale

          const response = await propertyService.getProperties(
            1, // page
            10, // pageSize
            statusParam,
            propertyTypeIdParam,
            locationIdParam,
            listingTypeParam,
            '' // No search
          );

          console.log('API Response (no search):', response);
          if (response.success) {
            console.log('Properties received:', response.data.properties);
            setProperties(response.data.properties);
            setPagination(response.data.pagination);
            if (response.data.statusCounts) {
              setStatusCounts(response.data.statusCounts);
              console.log('Status counts received:', response.data.statusCounts);
            }
          }
        } catch (error) {
          console.error('Error fetching properties:', error);
        } finally {
          setLoading(false);
        }
      };

      fetchAllProperties();
    }
  }, [searchTerm, debouncedSearch, typeFilter, statusFilter, locationFilter, listingTypeFilter, propertyTypes, locations, listingTypes]);

  useEffect(() => {
    const fetchFilters = async () => {
      try {
        const response = await propertyService.getPropertyFilters();
        if (response.success) {
          setPropertyTypes(response.data.propertyTypes.map(type => ({ ...type, name: type.name.charAt(0).toUpperCase() + type.name.slice(1) })));
          setLocations(response.data.locations);
          setAgents(response.data.agents);
          setStatuses(response.data.statuses);
          setListingTypes(response.data.listingTypes);
        }
      } catch (error) {
        console.error('Error fetching property filters:', error);
        // Keep empty arrays as fallback
      }
    };

    fetchFilters();
  }, []);

  // Properties are already filtered by backend
  const filteredProperties = properties;

  // Sort properties
  const sortedProperties = [...filteredProperties].sort((a, b) => {
    let aValue: any;
    let bValue: any;

    // Handle field name mapping
    if (sortBy === 'title') {
      aValue = a.name || a.title || '';
      bValue = b.name || b.title || '';
    } else if (sortBy === 'expiryDate') {
      aValue = a.expiryDate || a.expiry_date || '';
      bValue = b.expiryDate || b.expiry_date || '';
    } else {
      aValue = a[sortBy as keyof Property];
      bValue = b[sortBy as keyof Property];
    }

    if (sortBy === 'price') {
      aValue = Number(aValue);
      bValue = Number(bValue);
    }

    if (sortOrder === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  // Helper function to calculate stats from statusCounts
  const calculateStatsFromStatusCounts = (): PropertyStats => {
    if (statusCounts.length === 0) {
      // Fallback to manual calculation if statusCounts not available
      return {
        totalProperties: properties.length,
        activeProperties: properties.filter(p => p.status === 'Available').length,
        pendingProperties: properties.filter(p => p.status === 'Unpublished').length,
        blockedProperties: properties.filter(p => p.status === 'Blocked').length
      };
    }

    // Use statusCounts from API for accurate counts
    const totalProperties = pagination?.total || properties.length;

    // Find counts by both status_id and status_name for robustness
    const blockedCount = statusCounts.find(sc =>
      sc.status_id === 32 || sc.status_name?.toLowerCase() === 'blocked'
    )?.count || 0;

    const pendingCount = statusCounts.find(sc =>
      sc.status_id === 3 || sc.status_name?.toLowerCase() === 'pending' || sc.status_name?.toLowerCase() === 'unpublished'
    )?.count || 0;

    const availableCount = statusCounts.find(sc =>
      sc.status_name?.toLowerCase() === 'available'
    )?.count || 0;

    console.log('Status counts breakdown:', {
      totalProperties,
      availableCount,
      pendingCount,
      blockedCount,
      statusCounts
    });

    return {
      totalProperties,
      activeProperties: availableCount,
      pendingProperties: pendingCount,
      blockedProperties: blockedCount
    };
  };

  // Calculate stats
  const stats: PropertyStats = calculateStatsFromStatusCounts();

  // Check if filters are applied
  const hasFilters = searchTerm !== '' || typeFilter !== 'All Types' || statusFilter !== 'All Status' ||
                    locationFilter !== 'All Emirates' || listingTypeFilter !== 'All Types';

  // Event handlers
  const handleSelectProperty = (propertyId: number) => {
    const idStr = propertyId.toString();
    setSelectedProperties(prev =>
      prev.includes(idStr)
        ? prev.filter(id => id !== idStr)
        : [...prev, idStr]
    );
  };

  const handleSelectAll = (checked: boolean) => {
    setSelectedProperties(checked ? sortedProperties.map(p => p.id.toString()) : []);
  };

  const handleDeleteProperty = async (propertyId: number) => {
    try {
      console.log('Attempting to delete property with ID:', propertyId);
      const response = await propertyService.deleteProperty(propertyId);
      console.log('Delete API response:', response);

      if (response.success) {
        // Remove from local state
        const idStr = propertyId.toString();
        setProperties(prev => prev.filter(p => p.id !== propertyId));
        setSelectedProperties(prev => prev.filter(id => id !== idStr));
        showMessage('Property deleted successfully.', 'success');
      } else {
        console.error('Failed to delete property:', response.message);
        showMessage(`Failed to delete property: ${response.message}`, 'error');
      }
    } catch (error) {
      console.error('Error deleting property:', error);
      showMessage('An error occurred while deleting the property. Please try again.', 'error');
    }
  };

  const handleBlockProperty = (propertyId: number) => {
    setProperties(prev => prev.map(p =>
      p.id === propertyId ? { ...p, status: 'Blocked' } : p
    ));
  };

  const handleStatusChange = async (propertyId: number, status: Property['status'], reason?: string) => {
    try {
      // Convert frontend status to backend expected format (lowercase values)
      let backendStatus: string = '';
      if (!status) backendStatus = '';
      else {
        const s = String(status).toLowerCase();
        // Map frontend actions to backend status values
        if (s === 'unblock') backendStatus = 'unblock';
        else if (s === 'blocked') backendStatus = 'blocked';
        else if (s === 'unpublished') backendStatus = 'unpublished';
  else if (s === 'available') backendStatus = 'available';
  else if (s === 'publish' || s === 'published') backendStatus = 'publish';
        else backendStatus = s; // fallback to raw lowercase
      }

      console.log('handleStatusChange called:', { propertyId, status, reason, backendStatus });

      // Validate that reason is provided for blocking
      if (backendStatus === 'blocked' && !reason?.trim()) {
        showMessage('A reason is required when blocking a property.', 'error');
        return;
      }

      // Call the API to update status on backend
      const { updatePropertyStatus } = await import('@/lib/propertyService');
      console.log('Calling updatePropertyStatus with:', { propertyId: propertyId.toString(), backendStatus, reason });
      const result = await updatePropertyStatus(propertyId.toString(), backendStatus, reason);

      console.log('updatePropertyStatus result:', result);

      if ((result as any).success || (result as any).newStatus) {
        // Use the status from the API response if available
        const updatedProperty = (result as any).success ? (result as any).data : result;
        if (updatedProperty && updatedProperty.newStatus) {
          // Use newStatus from API response
          const apiStatus = updatedProperty.newStatus;
          const statusCount = statusCounts.find(sc =>
            sc.status_name?.toLowerCase() === apiStatus.toLowerCase()
          );
          const statusId = statusCount?.status_id;
          // Deterministically build the new properties array from current state so we can log it immediately
          const newProps = properties.map(p =>
            p.id === propertyId ? { ...p, status: apiStatus, status_name: apiStatus, status_id: statusId } : p
          );
          setProperties(newProps);
          console.log('Updated property in state (post-set):', newProps.find(p => p.id === propertyId));
          // Re-fetch properties from API to ensure UI matches backend
          try {
            const statusParam = statusFilter === 'All Status' ? '' : statusFilter;
            const propertyTypeIdParam = typeFilter === 'All Types' ? '' :
              propertyTypes.find(type => type.name === typeFilter)?.id?.toString() || '';
            const locationIdParam = locationFilter === 'All Emirates' ? '' :
              locations.find(location => location.name === locationFilter)?.id?.toString() || '';
            const listingTypeParam = listingTypeFilter === 'All Types' ? '' :
              listingTypes.find(type => type.name === listingTypeFilter)?.id?.toString() ||
              (listingTypeFilter === 'Rent' ? '43' : listingTypeFilter === 'Sale' ? '44' : '');

            const refreshed = await propertyService.getProperties(1, 10, statusParam, propertyTypeIdParam, locationIdParam, listingTypeParam, '');
            if (refreshed.success) {
              setProperties(refreshed.data.properties);
              setPagination(refreshed.data.pagination);
              if (refreshed.data.statusCounts) setStatusCounts(refreshed.data.statusCounts);
              console.log('Refreshed properties after status update:', refreshed.data.properties.find(p => p.id === propertyId));
            }
          } catch (err) {
            console.error('Error refreshing properties after status update:', err);
          }
          // Also call the configured PROPERTIES_API to ensure the endpoint is hit and logged
          try {
            const resp = await fetch(`${PROPERTIES_API}?page=1&pageSize=10`, { credentials: 'include' });
            const data = await resp.json();
            console.log('PROPERTIES_API response after single update:', data);
          } catch (e) {
            console.error('Error calling PROPERTIES_API endpoint after single update:', e);
          }
          showMessage(`Property status updated to ${apiStatus} successfully.`, 'success');
        } else {
          // Fallback to local update
          let newStatus: string;
          if (backendStatus === 'unblock') {
            newStatus = 'available';
          } else {
            newStatus = backendStatus;
          }
          const statusCount = statusCounts.find(sc =>
            sc.status_name?.toLowerCase() === newStatus.toLowerCase()
          );
          const statusId = statusCount?.status_id;

          const newPropsFallback = properties.map(p =>
            p.id === propertyId ? { ...p, status: newStatus, status_name: newStatus, status_id: statusId } : p
          );
          setProperties(newPropsFallback);
          console.log('Updated property in state (fallback):', newPropsFallback.find(p => p.id === propertyId));
        }

        // Show success notification
        showMessage(`Property status updated successfully.`, 'success');
      } else {
        // Show error notification
        showMessage('Failed to update property status. Please try again.', 'error');
      }
    } catch (error) {
      console.error('Error updating property status:', error);
      showMessage('An error occurred while updating property status.', 'error');
    }
  };

  const handleAddNote = async (propertyId: number, note: string) => {
    try {
      const result = await propertyService.addNote(propertyId, note);

      if (result.success) {
        showMessage('Note added successfully to the property.', 'success');
      } else {
        showMessage('Failed to add note. Please try again.', 'error');
      }
    } catch (error) {
      console.error('Error adding note to property:', error);
      showMessage('An error occurred while adding the note.', 'error');
    }
  };

  const handleBulkDelete = async () => {
    try {
      const selectedIds = selectedProperties.map(id => parseInt(id));
      console.log('Attempting to bulk delete properties with IDs:', selectedIds);

      if (selectedIds.length === 0) {
        showMessage('Please select properties to delete.', 'warning');
        return;
      }

      const response = await propertyService.bulkDeleteProperties(selectedIds);
      console.log('Bulk delete API response:', response);

      if (response.success) {
        // Remove from local state
        setProperties(prev => prev.filter(p => !selectedIds.includes(p.id)));
        setSelectedProperties([]);
        showMessage(`${selectedIds.length} properties deleted successfully.`, 'success');
      } else {
        console.error('Failed to bulk delete properties:', response.message);
        showMessage(`Failed to delete properties: ${response.message}`, 'error');
      }
    } catch (error) {
      console.error('Error bulk deleting properties:', error);
      showMessage('An error occurred while deleting the properties. Please try again.', 'error');
    }
  };

  const handleBulkBlock = async () => {
    if (selectedProperties.length === 0) {
      showMessage('Please select properties to block.', 'warning');
      return;
    }
    await handleBulkActionConfirm('block', '');
  };

  const handleBulkUnblock = async () => {
    if (selectedProperties.length === 0) {
      showMessage('Please select properties to unblock.', 'warning');
      return;
    }
    await handleBulkActionConfirm('unblock', '');
  };

    const handleBulkActionConfirm = async (actionType: 'block' | 'unblock', reason: string) => {
    try {
      const selectedIds = selectedProperties.map(id => parseInt(id));
      const actionStatus = actionType === 'block' ? 'blocked' : 'unblock'; // Send 'blocked' for block, 'unblock' for unblock
      const actionText = actionType === 'block' ? 'block' : 'unblock';

      console.log(`Attempting to bulk ${actionText} properties with IDs:`, selectedIds);

      // Call the bulk update API
      const { bulkUpdatePropertyStatus } = await import('@/lib/propertyService');
      const result = await bulkUpdatePropertyStatus(selectedIds, actionStatus);

      console.log(`Bulk ${actionText} API response:`, result);

      if (result.success) {
        // Use the status from the API response if available
        const updatedData = (result as any).data;
        if (updatedData && updatedData.changes) {
          // Update each property with its newStatus from API response
          setProperties(prev => prev.map(p => {
            const change = updatedData.changes.find((c: any) => c.propertyId === p.id);
            if (change) {
              const apiStatus = change.newStatus;
              const statusCount = statusCounts.find(sc =>
                sc.status_name?.toLowerCase() === apiStatus.toLowerCase()
              );
              const statusId = statusCount?.status_id;
              return { ...p, status: apiStatus, status_name: apiStatus, status_id: statusId };
            }
            return p;
          }));
          setSelectedProperties([]);
          showMessage(`${selectedIds.length} properties ${actionText}ed successfully.`, 'success');
          // Re-fetch properties to ensure UI matches backend
          try {
            const statusParam = statusFilter === 'All Status' ? '' : statusFilter;
            const propertyTypeIdParam = typeFilter === 'All Types' ? '' :
              propertyTypes.find(type => type.name === typeFilter)?.id?.toString() || '';
            const locationIdParam = locationFilter === 'All Emirates' ? '' :
              locations.find(location => location.name === locationFilter)?.id?.toString() || '';
            const listingTypeParam = listingTypeFilter === 'All Types' ? '' :
              listingTypes.find(type => type.name === listingTypeFilter)?.id?.toString() ||
              (listingTypeFilter === 'Rent' ? '43' : listingTypeFilter === 'Sale' ? '44' : '');

            const refreshed = await propertyService.getProperties(1, 10, statusParam, propertyTypeIdParam, locationIdParam, listingTypeParam, '');
            if (refreshed.success) {
              setProperties(refreshed.data.properties);
              setPagination(refreshed.data.pagination);
              if (refreshed.data.statusCounts) setStatusCounts(refreshed.data.statusCounts);
            }
          } catch (err) {
            console.error('Error refreshing properties after bulk status update:', err);
          }
        } else {
          // Fallback to local update
          const targetStatus = actionType === 'block' ? 'blocked' : 'available';
          const targetStatusCount = statusCounts.find(sc =>
            sc.status_name?.toLowerCase() === targetStatus
          );
          const targetStatusId = targetStatusCount?.status_id;

          // Update local state only if API call succeeds
          setProperties(prev => prev.map(p =>
            selectedIds.includes(p.id) ? {
              ...p,
              status: targetStatus,
              status_name: targetStatus,
              status_id: targetStatusId
            } : p
          ));
          setSelectedProperties([]);
          showMessage(`${selectedIds.length} properties ${actionText}ed successfully.`, 'success');
        }
      } else {
        console.error(`Failed to bulk ${actionText} properties:`, result.message);
        showMessage(`Failed to ${actionText} properties: ${result.message}`, 'error');
      }
    } catch (error) {
      console.error(`Error bulk ${actionType}ing properties:`, error);
      showMessage(`An error occurred while ${actionType}ing the properties. Please try again.`, 'error');
    }
  };

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const handleClearFilters = () => {
    setSearchTerm('');
    setTypeFilter('All Types');
    setStatusFilter('All Status');
    setLocationFilter('All Emirates');
    setListingTypeFilter('All Types');
  };

  return (
    <div className="space-y-6">
      <PropertyHeader
        totalProperties={stats.totalProperties}
        activeProperties={stats.activeProperties}
        pendingProperties={stats.pendingProperties}
        blockedProperties={stats.blockedProperties}
      />

      <PropertyFilters
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        typeFilter={typeFilter}
        setTypeFilter={setTypeFilter}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
        locationFilter={locationFilter}
        setLocationFilter={setLocationFilter}
        listingTypeFilter={listingTypeFilter}
        setListingTypeFilter={setListingTypeFilter}
        filteredCount={filteredProperties.length}
        totalCount={pagination?.total || properties.length}
        hasFilters={hasFilters}
        onClearFilters={handleClearFilters}
        propertyTypes={propertyTypes}
        locations={locations}
        agents={agents}
        statuses={statuses}
        listingTypes={listingTypes}
      />

      <PropertyTable
        properties={sortedProperties}
        selectedProperties={selectedProperties}
        onSelectProperty={handleSelectProperty}
        onSelectAll={handleSelectAll}
        onDeleteProperty={handleDeleteProperty}
        onBlockProperty={handleBlockProperty}
        onStatusChange={handleStatusChange}
        onAddNote={handleAddNote}
        onBulkDelete={handleBulkDelete}
        onBulkBlock={handleBulkBlock}
        onBulkUnblock={handleBulkUnblock}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSort={handleSort}
      />
    </div>
  );
};

export default PropertiesManagement;
