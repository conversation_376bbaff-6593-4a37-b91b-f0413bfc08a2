import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Users, Building, UserCheck } from 'lucide-react';

interface ActiveUsersStats {
  activeAgents: number;
  activeAgencies: number;
  activeWebUsers: number;
}

interface ActiveUsersCardProps {
  stats: ActiveUsersStats;
  onCardClick: (cardType: string) => void;
}

const ActiveUsersCard: React.FC<ActiveUsersCardProps> = ({ stats, onCardClick }) => {
  return (
    <Card className="mt-6">
      <CardHeader>
        <CardTitle className="text-xl font-semibold font-inter">Active Users</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div
            className="flex items-center space-x-4 hover:bg-gray-50 p-3 rounded-lg transition-colors"
          >
            <div className="bg-blue-100 p-3 rounded-full">
              <UserCheck className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Active Agents</p>
              <p className="text-2xl font-bold text-gray-900">{stats.activeAgents}</p>
            </div>
          </div>

          <div
            className="flex items-center space-x-4 hover:bg-gray-50 p-3 rounded-lg transition-colors"
          >
            <div className="bg-purple-100 p-3 rounded-full">
              <Building className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Active Agencies</p>
              <p className="text-2xl font-bold text-gray-900">{stats.activeAgencies}</p>
            </div>
          </div>

          <div
            className="flex items-center space-x-4 hover:bg-gray-50 p-3 rounded-lg transition-colors"
          >
            <div className="bg-green-100 p-3 rounded-full">
              <Users className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Web Users</p>
              <p className="text-2xl font-bold text-gray-900">{stats.activeWebUsers}</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ActiveUsersCard;
