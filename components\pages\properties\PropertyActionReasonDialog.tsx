
import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Property } from '@/utils/types/property';
import propertyService from '@/lib/propertyService';
import { PropertyHistoryEntry } from '@/lib/propertyService';

interface PropertyActionReasonDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (reason: string) => void;
  property: Property | null;
  actionType: 'block' | 'unpublish' | 'unblock';
}

const PropertyActionReasonDialog: React.FC<PropertyActionReasonDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  property,
  actionType
}) => {
  const [reason, setReason] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [history, setHistory] = useState<PropertyHistoryEntry[]>([]);
  const [historyLoading, setHistoryLoading] = useState(false);
  const { toast } = useToast();

  // Fetch property history when modal opens
  useEffect(() => {
    if (isOpen && property?.id) {
      const fetchHistory = async () => {
        setHistoryLoading(true);
        try {
          const response = await propertyService.getPropertyHistory(property.id);
          if (response.success && response.data) {
            setHistory(response.data);
          } else {
            console.error('Failed to fetch property history:', response.message);
          }
        } catch (error) {
          console.error('Error fetching property history:', error);
        } finally {
          setHistoryLoading(false);
        }
      };

      fetchHistory();
    }
  }, [isOpen, property?.id]);

  const handleConfirm = async () => {
    if (!reason.trim()) {
      toast({
        title: "Reason Required",
        description: "Please provide a reason for this action.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      // Simulate sending email to agent/agency
      console.log('Sending email notification:', {
        propertyId: property?.id,
        propertyTitle: property?.title,
        agentName: property?.agentName,
        agencyName: property?.agencyName,
        actionType,
        reason: reason.trim()
      });

      // In a real implementation, this would call an API to send the email
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast({
        title: "Action Completed",
        description: `Property has been ${actionType === 'block' ? 'blocked' : actionType === 'unblock' ? 'unblocked' : 'unpublished'} and the agent has been notified via email.`,
      });

      onConfirm(reason.trim());
      setReason('');
      onClose();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to complete the action. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setReason('');
    setHistory([]);
    onClose();
  };

  const actionText = actionType === 'block' ? 'block' : actionType === 'unblock' ? 'unblock' : 'unpublish';
  const actionTitle = actionType === 'block' ? 'Block Property' : actionType === 'unblock' ? 'Unblock Property' : 'Unpublish Property';

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>{actionTitle}</DialogTitle>
          <DialogDescription>
            You are about to {actionText} the property: <strong>{property?.title}</strong>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 p-6">
          <div className="bg-yellow-50 p-3 rounded-md">
            <p className="text-sm text-yellow-800">
              The Agency ({property?.agencyName}) will be notified via email with your reason.
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="reason">Reason for {actionText}ing this property</Label>
            <Textarea
              id="reason"
              placeholder={`Please provide a detailed reason for ${actionText}ing this property...`}
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              rows={4}
            />
          </div>

          {/* Property History Section */}
          <div className="border-t border-gray-200 pt-4">
            <h3 className="mb-3 text-sm font-semibold text-gray-900">Property History ({history.length})</h3>
            <div className="max-h-60 overflow-y-auto space-y-3">
              {historyLoading ? (
                <div className="flex items-center justify-center py-4">
                  <div className="text-sm text-gray-500">Loading history...</div>
                </div>
              ) : history.length > 0 ? (
                history.map((entry, index) => (
                  <div key={`${entry.id}-${index}`} className="rounded-lg bg-gray-50 p-3">
                    <div className="mb-2 flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        <span
                          className={`rounded-full px-2 py-1 text-xs font-medium ${
                            entry.action === 'status_update'
                              ? 'bg-blue-100 text-blue-800'
                              : entry.action === 'restore'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {entry.action === 'status_update'
                            ? 'Status Change'
                            : entry.action === 'restore'
                            ? 'Restore'
                            : entry.action?.charAt(0).toUpperCase() + entry.action?.slice(1)}
                        </span>
                        <span className="text-sm font-medium text-gray-900">{entry.adminName}</span>
                      </div>
                      <span className="text-xs text-gray-500">{new Date(entry.createdAt).toLocaleString()}</span>
                    </div>
                    <p className="text-sm text-gray-600">
                      {entry.action === 'status_update'
                        ? `Status changed from ${entry.previousStatusName} to ${entry.newStatusName}`
                        : entry.notes || `Action: ${entry.action}`}
                    </p>
                  </div>
                ))
              ) : (
                <div className="flex items-center justify-center py-6">
                  <div className="text-center">
                    <svg className="mx-auto mb-2 h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                    <p className="text-sm text-gray-500">No history available</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button
            variant={actionType === 'block' ? 'destructive' : actionType === 'unblock' ? 'default' : 'secondary'}
            onClick={handleConfirm}
            disabled={isLoading || !reason.trim()}
          >
            {isLoading ? 'Processing...' : `${actionTitle}`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PropertyActionReasonDialog;
