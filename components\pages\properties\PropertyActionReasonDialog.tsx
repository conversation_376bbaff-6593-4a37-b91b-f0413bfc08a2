
import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Property } from '@/utils/types/property';

interface PropertyActionReasonDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (reason: string) => void;
  property: Property | null;
  actionType: 'block' | 'unpublish' | 'unblock' | 'publish';
}

const PropertyActionReasonDialog: React.FC<PropertyActionReasonDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  property,
  actionType
}) => {
  const [reason, setReason] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleConfirm = async () => {
    if (!reason.trim()) {
      toast({
        title: "Reason Required",
        description: "Please provide a reason for this action.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      // Simulate sending email to agent/agency
      console.log('Sending email notification:', {
        propertyId: property?.id,
        propertyTitle: property?.title,
        agentName: property?.agentName,
        agencyName: property?.agencyName,
        actionType,
        reason: reason.trim()
      });

      // In a real implementation, this would call an API to send the email
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast({
        title: "Action Completed",
        description: `Property has been ${actionType === 'block' ? 'blocked' : actionType === 'unblock' ? 'unblocked' : 'unpublished'} and the agent has been notified via email.`,
      });

      onConfirm(reason.trim());
      setReason('');
      onClose();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to complete the action. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setReason('');
    onClose();
  };

  const actionText = actionType === 'block' ? 'block' : actionType === 'unblock' ? 'unblock' : actionType === 'publish' ? 'publish' : 'unpublish';
  const actionTitle = actionType === 'block' ? 'Block Property' : actionType === 'unblock' ? 'Unblock Property' : actionType === 'publish' ? 'Publish Property' : 'Unpublish Property';

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>{actionTitle}</DialogTitle>
          <DialogDescription>
            You are about to {actionText} the property: <strong>{property?.title}</strong>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 p-6">
          <div className="bg-yellow-50 p-3 rounded-md">
            <p className="text-sm text-yellow-800">
              The agent/agency ({property?.agentName} - {property?.agencyName}) will be notified via email with your reason.
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="reason">Reason for {actionText}ing this property</Label>
            <Textarea
              id="reason"
              placeholder={`Please provide a detailed reason for ${actionText}ing this property...`}
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              rows={4}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button
            variant={actionType === 'block' ? 'destructive' : actionType === 'unblock' ? 'default' : 'secondary'}
            onClick={handleConfirm}
            disabled={isLoading || !reason.trim()}
          >
            {isLoading ? 'Processing...' : `${actionTitle}`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PropertyActionReasonDialog;
