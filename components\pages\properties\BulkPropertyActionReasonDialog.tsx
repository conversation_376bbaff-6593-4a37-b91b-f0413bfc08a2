import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';

interface BulkPropertyActionReasonDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (reason: string) => void;
  selectedCount: number;
  actionType: 'block' | 'unpublish' | 'unblock' | 'publish';
}

const BulkPropertyActionReasonDialog: React.FC<BulkPropertyActionReasonDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  selectedCount,
  actionType
}) => {
  const [reason, setReason] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleConfirm = async () => {
    if (!reason.trim()) {
      toast({
        title: "Reason Required",
        description: "Please provide a reason for this action.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      // Log the bulk action for debugging
      console.log('Bulk property action:', {
        selectedCount,
        actionType,
        reason: reason.trim()
      });

      // In a real implementation, this would call an API to send emails to all affected agents
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast({
        title: "Bulk Action Completed",
        description: `${selectedCount} properties have been ${actionType === 'block' ? 'blocked' : actionType === 'unblock' ? 'unblocked' : 'unpublished'} and the agents have been notified via email.`,
      });

      onConfirm(reason.trim());
      setReason('');
      onClose();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to complete the bulk action. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setReason('');
    onClose();
  };

  const actionText = actionType === 'block' ? 'block' : actionType === 'unblock' ? 'unblock' : actionType === 'publish' ? 'publish' : 'unpublish';
  const actionTitle = actionType === 'block' ? 'Block Properties' : actionType === 'unblock' ? 'Unblock Properties' : actionType === 'publish' ? 'Publish Properties' : 'Unpublish Properties';

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>{actionTitle}</DialogTitle>
          <DialogDescription>
            You are about to {actionText} <strong>{selectedCount}</strong> selected properties.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 p-6">
          <div className="bg-yellow-50 p-3 rounded-md">
            <p className="text-sm text-yellow-800">
              All affected agents/agencies will be notified via email with your reason for this action.
            </p>
          </div>

          <div className="bg-red-50 p-3 rounded-md">
            <p className="text-sm text-red-800">
              <strong>Warning:</strong> This action will affect {selectedCount} properties. Please ensure you have selected the correct properties.
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="bulk-reason">Reason for {actionText}ing these properties</Label>
            <Textarea
              id="bulk-reason"
              placeholder={`Please provide a detailed reason for ${actionText}ing these ${selectedCount} properties...`}
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              rows={4}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button
            variant={actionType === 'block' ? 'destructive' : actionType === 'unblock' ? 'default' : 'secondary'}
            onClick={handleConfirm}
            disabled={isLoading || !reason.trim()}
          >
            {isLoading ? 'Processing...' : `${actionTitle} (${selectedCount})`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default BulkPropertyActionReasonDialog;
