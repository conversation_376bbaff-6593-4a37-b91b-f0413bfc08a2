// Small debounce utility used across components to avoid duplication
export default function debounce<T extends (...args: any[]) => any>(func: T, delay = 300) {
  let timer: ReturnType<typeof setTimeout> | null = null;
  const debounced = (...args: Parameters<T>) => {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => func(...args), delay);
  };
  // Provide a cancel method similar to the previous inline implementation
  (debounced as any).cancel = () => {
    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
  };
  return debounced as (...args: Parameters<T>) => void & { cancel?: () => void };
}
