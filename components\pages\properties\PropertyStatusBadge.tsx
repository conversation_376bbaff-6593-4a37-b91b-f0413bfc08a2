import React from 'react';
import { CheckCircle, XCircle, Eye } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Property } from '@/utils/types/property';

interface PropertyStatusBadgeProps {
  status: Property['status'];
  onStatusChange?: (newStatus: Property['status']) => void;
}

const PropertyStatusBadge: React.FC<PropertyStatusBadgeProps> = ({ status, onStatusChange }) => {
  // Normalize status to a consistent lowercase form for comparisons
  const normalizeStatus = (s?: string) => (s ? String(s).toLowerCase().trim() : '');

  // Map of normalized status -> display config
  const variants: Record<string, { variant: 'default' | 'secondary' | 'destructive'; icon: React.ComponentType<any>; label?: string }> = {
    available: { variant: 'default', icon: CheckCircle, label: 'Available' },
    publish: { variant: 'default', icon: CheckCircle, label: 'Publish' },
    published: { variant: 'default', icon: CheckCircle, label: 'Publish' },
    sold: { variant: 'default', icon: CheckCircle, label: 'Sold' },
    rented: { variant: 'default', icon: CheckCircle, label: 'Rented' },
    unpublished: { variant: 'secondary', icon: Eye, label: 'Unpublished' },
    blocked: { variant: 'destructive', icon: XCircle, label: 'Blocked' },
    unblock: { variant: 'default', icon: CheckCircle, label: 'Unblock' },
    unblocked: { variant: 'default', icon: CheckCircle, label: 'Unblocked' },
  };

  const statusOptions: Array<{ label: string; value: string; icon: React.ComponentType<any> }> = [
    // Include 'Available' so users can set status back to Available from the dropdown
    { label: 'Available', value: 'available', icon: CheckCircle },
    { label: 'Sold', value: 'sold', icon: CheckCircle },
    { label: 'Rented', value: 'rented', icon: CheckCircle },
    { label: 'Blocked', value: 'blocked', icon: XCircle },
  ];

  // Normalize the current status for lookup
  const normalizedStatus = status ? normalizeStatus(status) : '';

  // Decide display config; fall back to unpublished-like default
  const config = variants[normalizedStatus] || { variant: 'secondary' as const, icon: Eye, label: undefined };
  const Icon = config.icon;

  const capitalize = (s: string) => (s ? s.charAt(0).toUpperCase() + s.slice(1) : s);

  // Dynamic status options based on current status
  const getStatusOptions = () => {
    const baseOptions = [...statusOptions];

  // Treat only 'publish' and 'published' as the canonical published states
  // (do not treat 'available' as 'published' to avoid showing Unpublish on Available)
  const isCurrentlyPublished = ['publish', 'published'].includes(normalizedStatus);

    if (isCurrentlyPublished) {
      baseOptions.push({ label: 'Unpublish', value: 'unpublished', icon: Eye });
    } else {
      baseOptions.push({ label: 'Publish', value: 'publish', icon: CheckCircle });
    }

    // If currently blocked, offer Unblock
    if (normalizedStatus === 'blocked') {
      baseOptions.push({ label: 'Unblock', value: 'unblock', icon: CheckCircle });
    }

    return baseOptions;
  };

  const dynamicStatusOptions = getStatusOptions();
  // Remove any option that equals the current status (so you don't see the active status as an action)
  const filteredStatusOptions = dynamicStatusOptions.filter(opt => {
    const optNorm = normalizeStatus(opt.value);
    return optNorm !== normalizedStatus;
  });

  if (!onStatusChange) {
    return (
        <Badge variant={config.variant} className="flex items-center gap-1">
          <Icon className="w-3 h-3" />
          {config.label || (normalizedStatus ? capitalize(normalizedStatus) : 'Unknown')}
        </Badge>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="cursor-pointer">
          <Badge variant={config.variant} className="flex items-center gap-1 hover:opacity-80 transition-opacity">
            <Icon className="w-3 h-3" />
            {config.label || (normalizedStatus ? capitalize(normalizedStatus) : 'Unknown')}
          </Badge>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="bg-white border shadow-lg z-50">
        {filteredStatusOptions.map((statusOption) => (
          <DropdownMenuItem
            key={statusOption.value}
            onClick={() => onStatusChange(statusOption.value)}
            className="flex items-center gap-2 cursor-pointer hover:bg-gray-100"
          >
            {React.createElement(statusOption.icon, { className: 'w-4 h-4' })}
            {statusOption.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default PropertyStatusBadge;
