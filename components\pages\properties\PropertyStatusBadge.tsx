import React from 'react';
import { CheckCircle, XCircle, Eye } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Property } from '@/utils/types/property';

interface PropertyStatusBadgeProps {
  status: Property['status'];
  onStatusChange?: (newStatus: Property['status']) => void;
}

const PropertyStatusBadge: React.FC<PropertyStatusBadgeProps> = ({ status, onStatusChange }) => {
  // Normalize status to handle both lowercase and capitalized formats
  const normalizeStatus = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
  };

  const variants: Record<string, { variant: 'default' | 'secondary' | 'destructive', icon: React.ComponentType<any> }> = {
    Available: { variant: 'default', icon: CheckCircle },
    Sold: { variant: 'default', icon: CheckCircle },
    Rented: { variant: 'default', icon: CheckCircle },
    Unpublished: { variant: 'secondary', icon: Eye },
    Blocked: { variant: 'destructive', icon: XCircle },
    unblock: { variant: 'default', icon: CheckCircle },
    unblocked: { variant: 'default', icon: CheckCircle }
  };

  const statusOptions: string[] = ['Available', 'Sold', 'Rented', 'Unpublished', 'Blocked'];

  // Normalize the current status for lookup
  const normalizedStatus = status ? normalizeStatus(status) : '';
  const config = (normalizedStatus && variants[normalizedStatus]) || { variant: 'secondary' as const, icon: Eye };
  const Icon = config.icon;

  // Dynamic status options based on current status
  const getStatusOptions = () => {
    if (normalizedStatus === 'Blocked') {
      // When blocked, show "Unblock" option instead of "Blocked"
      return [
        { label: 'Available', value: 'Available' },
        { label: 'Sold', value: 'Sold' },
        { label: 'Rented', value: 'Rented' },
        { label: 'Unpublished', value: 'Unpublished' },
        { label: 'Unblock', value: 'unblock' } // Unblock changes status to Available
      ];
    }
    return statusOptions.map(option => ({ label: option, value: option }));
  };

  const dynamicStatusOptions = getStatusOptions();

  if (!onStatusChange) {
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="w-3 h-3" />
        {normalizedStatus || 'Unknown'}
      </Badge>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="cursor-pointer">
          <Badge variant={config.variant} className="flex items-center gap-1 hover:opacity-80 transition-opacity">
            <Icon className="w-3 h-3" />
            {normalizedStatus || 'Unknown'}
          </Badge>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="bg-white border shadow-lg z-50">
        {dynamicStatusOptions.map((statusOption) => (
          <DropdownMenuItem
            key={statusOption.value}
            onClick={() => onStatusChange(statusOption.value)}
            className="flex items-center gap-2 cursor-pointer hover:bg-gray-100"
          >
            {React.createElement(variants[statusOption.value].icon, { className: "w-4 h-4" })}
            {statusOption.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default PropertyStatusBadge;
