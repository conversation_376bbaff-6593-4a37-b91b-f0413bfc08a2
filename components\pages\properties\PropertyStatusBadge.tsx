import React from 'react';
import { CheckCircle, XCircle, Eye } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Property } from '@/utils/types/property';

interface PropertyStatusBadgeProps {
  status: Property['status'];
  onStatusChange?: (newStatus: Property['status']) => void;
}

const PropertyStatusBadge: React.FC<PropertyStatusBadgeProps> = ({ status, onStatusChange }) => {
  // Normalize status to handle both lowercase and capitalized formats
  const normalizeStatus = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
  };

  const variants: Record<string, { variant: 'default' | 'secondary' | 'destructive', icon: React.ComponentType<any> }> = {
    Available: { variant: 'default', icon: CheckCircle },
    Sold: { variant: 'default', icon: CheckCircle },
    Rented: { variant: 'default', icon: CheckCircle },
    Unpublished: { variant: 'secondary', icon: Eye },
    Blocked: { variant: 'destructive', icon: XCircle },
    unblock: { variant: 'default', icon: CheckCircle },
    unblocked: { variant: 'default', icon: CheckCircle }
  };

  const statusOptions: Array<{ label: string; value: string; icon: React.ComponentType<any> }> = [
    { label: 'Available', value: 'Available', icon: CheckCircle },
    { label: 'Sold', value: 'Sold', icon: CheckCircle },
    { label: 'Rented', value: 'Rented', icon: CheckCircle },
    // Unpublished/Publish handled dynamically
    { label: 'Blocked', value: 'Blocked', icon: XCircle },
  ];

  // Normalize the current status for lookup
  const normalizedStatus = status ? normalizeStatus(status) : '';
  const config = (normalizedStatus && variants[normalizedStatus]) || { variant: 'secondary' as const, icon: Eye };
  const Icon = config.icon;

  // Dynamic status options based on current status
  const getStatusOptions = () => {
    // When blocked, show Unblock action alongside other options
    const baseOptions = [...statusOptions];

    // Determine publish/unpublish option depending on current status
    if (normalizedStatus === 'Unpublished') {
      // If currently unpublished, show Publish (maps to 'available')
      baseOptions.push({ label: 'Publish', value: 'available', icon: CheckCircle });
    } else {
      // Otherwise show Unpublish action
      baseOptions.push({ label: 'Unpublish', value: 'unpublished', icon: Eye });
    }

    if (normalizedStatus === 'Blocked') {
      // If blocked, provide Unblock action
      baseOptions.push({ label: 'Unblock', value: 'unblock', icon: CheckCircle });
    }

    return baseOptions;
  };

  const dynamicStatusOptions = getStatusOptions();

  if (!onStatusChange) {
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="w-3 h-3" />
        {normalizedStatus || 'Unknown'}
      </Badge>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="cursor-pointer">
          <Badge variant={config.variant} className="flex items-center gap-1 hover:opacity-80 transition-opacity">
            <Icon className="w-3 h-3" />
            {normalizedStatus || 'Unknown'}
          </Badge>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="bg-white border shadow-lg z-50">
        {dynamicStatusOptions.map((statusOption) => (
          <DropdownMenuItem
            key={statusOption.value}
            onClick={() => onStatusChange(statusOption.value)}
            className="flex items-center gap-2 cursor-pointer hover:bg-gray-100"
          >
            {React.createElement(statusOption.icon, { className: 'w-4 h-4' })}
            {statusOption.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default PropertyStatusBadge;
