import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

// Types for the request body
interface BulkStatusUpdateRequest {
  ids: number[];
  status: string;
  reason?: string;
}

// Types for the API response
interface ApiResponse {
  status: number;
  success: boolean;
  message: string;
  data?: any;
}

// Authentication helper
async function getAuthToken(): Promise<string | null> {
  const cookieStore = await cookies();
  const authToken = cookieStore.get('adminAuthToken');
  return authToken?.value || null;
}

// Forward request to external API
async function forwardToExternalAPI(
  request: NextRequest,
  body: BulkStatusUpdateRequest
): Promise<ApiResponse> {
  const authToken = await getAuthToken();

  if (!authToken) {
    return {
      status: 401,
      success: false,
      message: 'Authentication required'
    };
  }

  const externalApiUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://api.findanyagent.ae';
  const endpoint = `${externalApiUrl}/api/v1/admin/properties/bulk/status`;

  try {
    const response = await fetch(endpoint, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `adminAuthToken=${authToken}`,
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      return {
        status: response.status,
        success: false,
        message: `External API error: ${response.statusText}`
      };
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error forwarding to external API:', error);
    return {
      status: 500,
      success: false,
      message: 'Failed to connect to external API'
    };
  }
}

// Validation helper
function validateRequest(body: any): { isValid: boolean; error?: string } {
  if (!body.ids || !Array.isArray(body.ids) || body.ids.length === 0) {
    return { isValid: false, error: 'Property IDs are required and must be a non-empty array' };
  }

  if (!body.status || typeof body.status !== 'string') {
    return { isValid: false, error: 'Status is required and must be a string' };
  }

  // Validate that all IDs are numbers
  if (!body.ids.every((id: any) => typeof id === 'number' && id > 0)) {
    return { isValid: false, error: 'All property IDs must be positive numbers' };
  }

  // For blocking status, reason is required
  if (body.status.toLowerCase() === 'blocked' && (!body.reason || body.reason.trim() === '')) {
    return { isValid: false, error: 'Reason is required when blocking properties' };
  }

  // For unblocking (changing from blocked to available), reason is also required
  if (body.status.toLowerCase() === 'available' && (!body.reason || body.reason.trim() === '')) {
    return { isValid: false, error: 'Reason is required when unblocking properties' };
  }

  return { isValid: true };
}

// PUT handler for bulk property status update
export async function PUT(request: NextRequest) {
  try {
    // Parse request body
    const body: BulkStatusUpdateRequest = await request.json();

    // Validate request
    const validation = validateRequest(body);
    if (!validation.isValid) {
      return NextResponse.json(
        {
          status: 400,
          success: false,
          message: validation.error
        },
        { status: 400 }
      );
    }

    // Log the request for debugging
    console.log('Bulk property status update request:', {
      ids: body.ids,
      status: body.status,
      reason: body.reason ? '[REDACTED]' : undefined,
      count: body.ids.length
    });

    // Forward to external API
    const result = await forwardToExternalAPI(request, body);

    // Return the response from external API
    return NextResponse.json(result, { status: result.status });

  } catch (error) {
    console.error('Error in bulk property status update:', error);

    return NextResponse.json(
      {
        status: 500,
        success: false,
        message: 'Internal server error'
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    {
      status: 405,
      success: false,
      message: 'Method not allowed. Use PUT for bulk status updates.'
    },
    { status: 405 }
  );
}

export async function POST() {
  return NextResponse.json(
    {
      status: 405,
      success: false,
      message: 'Method not allowed. Use PUT for bulk status updates.'
    },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    {
      status: 405,
      success: false,
      message: 'Method not allowed. Use PUT for bulk status updates.'
    },
    { status: 405 }
  );
}
