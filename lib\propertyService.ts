import {
    PROPERTIES_API,
    PROPERTIES_STATS_API,
    PROPERTIES_FILTERS_API,
    PROPERTY_DELETE_API,
    PROPERTY_UPDATE_STATUS_API,
    PROPERTY_BULK_DELETE_API,
    PROPERTY_BULK_STATUS_API,
    PROPERTY_ADD_NOTE_API,
    PROPERTY_HIDE_API,
    PROPERTY_RESTORE_API,
    PROPERTY_FEATURE_API,
    PROPERTY_VERIFY_API,
    PROPERTY_HISTORY_API,
} from '@/app/lib/apiRoutes';
import { STATUS_API } from '@/app/lib/apiRoutes';
import { Property, PropertyPagination, PropertiesResponse, PropertyStatusCount, PropertyNote as PropertyNoteType } from '@/utils/types/property';

// Local lightweight types for notes/history returned by property APIs
export interface PropertyHistoryEntry {
    id: number;
    action: string;
    previousStatus?: number;
    previousStatusName?: string;
    newStatus?: number;
    newStatusName?: string;
    notes?: string | null;
    createdAt?: string | number | null;
    created_at?: string | number | null;
    adminName?: string;
    adminFirstName?: string;
    adminLastName?: string;
    [key: string]: any;
}

export type PropertyNote = PropertyNoteType;

class PropertyService {
    private getAuthHeaders() {
        return { 'Content-Type': 'application/json' };
    }

    private transformProperty(apiProperty: any): Property {
        return {
            id: apiProperty.id,
            name: apiProperty.name,
            title: apiProperty.name,
            price: apiProperty.price,
            size: apiProperty.size,
            area: apiProperty.size,
            listing_type: apiProperty.listing_type,
            status_id: apiProperty.status_id,
            status_name: apiProperty.status_name,
            status: apiProperty.status_name,
            is_featured: apiProperty.is_featured,
            featured: apiProperty.is_featured || false,
            is_verified: apiProperty.is_verified,
            verified: apiProperty.is_verified || false,
            expiry_date: apiProperty.expiry_date,
            expiryDate: apiProperty.expiry_date,
            slug: apiProperty.slug,
            meta_title: apiProperty.meta_title,
            images: apiProperty.images,
            type: apiProperty.property_type_name,
            location_name: apiProperty.location_name,
            agentName: 'N/A',
            agencyName: apiProperty.agency_name,
        } as Property;
    }

    private transformPagination(apiPagination: any): PropertyPagination {
        return {
            total: apiPagination.total,
            totalPages: apiPagination.totalPages,
            currentPage: apiPagination.currentPage,
            perPage: apiPagination.perPage,
            page: apiPagination.currentPage,
            pageSize: apiPagination.perPage,
            hasNext: apiPagination.currentPage < apiPagination.totalPages,
            hasPrev: apiPagination.currentPage > 1,
        };
    }

    async getProperties(page = 1, pageSize = 10, status?: string, propertyTypeId?: string, locationId?: string, listingType?: string, search?: string): Promise<PropertiesResponse> {
        const params = new URLSearchParams({ page: String(page), pageSize: String(pageSize) });
        if (status) params.append('status', status);
        if (propertyTypeId) params.append('propertyTypeId', propertyTypeId);
        if (locationId) params.append('locationId', locationId);
        if (listingType) params.append('listingType', listingType);
        if (search) params.append('search', search);

        const res = await fetch(`${PROPERTIES_API}?${params.toString()}`, { method: 'GET', headers: this.getAuthHeaders(), credentials: 'include' });
        if (!res.ok) throw new Error(`HTTP error! status: ${res.status}`);
        const apiData = await res.json();

        return {
            status: apiData.status,
            success: apiData.success,
            message: apiData.message,
            data: {
                properties: (apiData.data.properties || []).map((p: any) => this.transformProperty(p)),
                pagination: this.transformPagination(apiData.data.pagination || { total: 0, totalPages: 0, currentPage: 1, perPage: 10 }),
                statusCounts: apiData.data.statusCounts,
            },
        } as PropertiesResponse;
    }

    async getPropertyFilters() {
        const res = await fetch(PROPERTIES_FILTERS_API, { method: 'GET', headers: this.getAuthHeaders(), credentials: 'include' });
        if (!res.ok) throw new Error(`HTTP error! status: ${res.status}`);
        return await res.json();
    }

    // minimal wrappers for other endpoints (keep existing behavior)
    async deleteProperty(id: number) { const res = await fetch(PROPERTY_DELETE_API(id), { method: 'DELETE', headers: this.getAuthHeaders(), credentials: 'include' }); return await res.json(); }
    async addNote(id: number, note: string) {
        const fd = new FormData();
        fd.append('note', note);
        const res = await fetch(PROPERTY_ADD_NOTE_API(id), { method: 'POST', credentials: 'include', body: fd });
        if (!res.ok) {
            const errorText = await res.text();
            console.error('Failed to add note:', errorText);
            return { success: false, status: res.status, errorText };
        }
        return await res.json();
    }
    // Fetch notes for a property (GET /admin/properties/:id/notes)
    async getNotes(id: number) {
        try {
            const res = await fetch(PROPERTY_ADD_NOTE_API(id), { method: 'GET', headers: this.getAuthHeaders(), credentials: 'include' });
            if (!res.ok) {
                const errorText = await res.text();
                console.error('Failed to fetch notes:', errorText);
                return { success: false, status: res.status, errorText };
            }
            return await res.json();
        } catch (error) {
            console.error('Error fetching notes:', error);
            return { success: false, error };
        }
    }
    async getPropertyHistory(propertyId: number) { const res = await fetch(PROPERTY_HISTORY_API(propertyId), { method: 'GET', headers: this.getAuthHeaders(), credentials: 'include' }); return await res.json(); }
}

export default new PropertyService();

// Standalone helper: update the status of a single property and return parsed API JSON
export async function updatePropertyStatus(propertyId: string, statusValue: string, reason?: string): Promise<any> {
    try {
        const formData = new FormData();
        formData.append('status', statusValue);
        if (reason) formData.append('reason', reason);

        const response = await fetch(`${PROPERTIES_API}/${propertyId}/status`, { method: 'PUT', credentials: 'include', body: formData });
        if (!response.ok) {
            const errorText = await response.text();
            console.error('Failed to update property status:', errorText);
            return { success: false, status: response.status, errorText };
        }
        return await response.json();
    } catch (error) {
        console.error('Error updating property status:', error);
        return { success: false, error };
    }
}

export async function bulkUpdatePropertyStatus(ids: number[], status: string, reason?: string): Promise<any> {
    try {
        const response = await fetch(PROPERTY_BULK_STATUS_API, { method: 'PUT', headers: { 'Content-Type': 'application/json' }, credentials: 'include', body: JSON.stringify({ ids, status, reason }) });
        if (!response.ok) {
            const errorText = await response.text();
            console.error('Failed to bulk update property status:', errorText);
            return { success: false, status: response.status, errorText };
        }
        return await response.json();
    } catch (error) {
        console.error('Error bulk updating property status:', error);
        return { success: false, error };
    }
}

export type { Property, PropertiesResponse };
