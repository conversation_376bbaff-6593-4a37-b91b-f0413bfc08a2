import {
    PROPERTIES_API,
    PROPERTIES_STATS_API,
    PROPERTIES_FILTERS_API,
    PROPERTY_DELETE_API,
    PROPERTY_UPDATE_STATUS_API,
    PROPERTY_BULK_DELETE_API,
    PROPERTY_BULK_STATUS_API,
    PROPERTY_ADD_NOTE_API,
    PROPERTY_HIDE_API,
    PROPERTY_RESTORE_API,
    PROPERTY_FEATURE_API,
    PROPERTY_VERIFY_API
} from '@/app/lib/apiRoutes';
import { Property, PropertyPagination, PropertiesResponse, PropertyStatusCount } from '@/utils/types/property';

interface ApiProperty {
    id: number;
    name: string;
    price: string;
    size: string;
    location_id: number;
    location_name: string;
    property_type_name: string;
    agency_name: string;
    listing_type: number;
    status_id: number;
    status_name: string;
    is_featured: boolean | null;
    is_verified: boolean | null;
    expiry_date: string;
    slug: string;
    meta_title: string;
    images: Array<{
        id: number;
        url: string;
    }>;
}

interface ApiPropertiesResponse {
    status: number;
    success: boolean;
    message: string;
    data: {
        properties: ApiProperty[];
        pagination: {
            total: number;
            totalPages: number;
            currentPage: number;
            perPage: number;
        };
        statusCounts: Array<{
            status_id: number;
            status_name: string;
            count: number;
        }>;
    };
}

interface PropertyFiltersResponse {
    status: number;
    success: boolean;
    message: string;
    data: {
        propertyTypes: Array<{ id: number; name: string }>;
        locations: Array<{ id: number; name: string }>;
        listingTypes: Array<{ id: number; name: string }>;
        agents: Array<{ id: number; name: string }>;
        statuses: Array<{ id: number; name: string }>;
    };
}

interface ApiResponse {
    status: number;
    success: boolean;
    message: string;
    data?: any;
}

interface PropertyStats {
    totalProperties: number;
    availableProperties: number;
    soldProperties: number;
    rentedProperties: number;
    unpublishedProperties: number;
    blockedProperties: number;
    featuredProperties: number;
    verifiedProperties: number;
    // Optional fields that might be in API response
    total?: number;
    available?: number;
    sold?: number;
    rented?: number;
    unpublished?: number;
    blocked?: number;
    featured?: number;
    verified?: number;
}

interface PropertyStatsResponse {
    status: number;
    success: boolean;
    message: string;
    data: PropertyStats;
}

interface PropertyNote {
    id: number;
    note: string;
    createdAt: string;
    adminName: string;
}

interface PropertyHistoryEntry {
    id: number;
    action: string;
    previousStatus: number;
    newStatus: number;
    previousStatusName: string;
    newStatusName: string;
    notes: string | null;
    createdAt: string;
    adminName: string;
}

class PropertyService {
    private getAuthHeaders() {
        return {
            'Content-Type': 'application/json',
        };
    }

    private transformProperty(apiProperty: ApiProperty): Property {
        return {
            id: apiProperty.id,
            name: apiProperty.name,
            title: apiProperty.name, // Map name to title for compatibility
            price: apiProperty.price,
            size: apiProperty.size,
            area: apiProperty.size, // Map size to area for compatibility
            listing_type: apiProperty.listing_type,
            status_id: apiProperty.status_id,
            status_name: apiProperty.status_name,
            status: apiProperty.status_name, // Map status_name to status for compatibility
            is_featured: apiProperty.is_featured,
            featured: apiProperty.is_featured || false, // Map is_featured to featured
            is_verified: apiProperty.is_verified,
            verified: apiProperty.is_verified || false, // Map is_verified to verified
            expiry_date: apiProperty.expiry_date,
            expiryDate: apiProperty.expiry_date, // Map expiry_date to expiryDate
            slug: apiProperty.slug,
            meta_title: apiProperty.meta_title,
            images: apiProperty.images,
            // Set default values for fields not in API
            type: apiProperty.property_type_name, // Default type
            location_name: apiProperty.location_name,
            agentName: 'N/A', // Default agent name
            agencyName: apiProperty.agency_name, // Default agency name
        };
    }

    private transformPagination(apiPagination: any): PropertyPagination {
        return {
            total: apiPagination.total,
            totalPages: apiPagination.totalPages,
            currentPage: apiPagination.currentPage,
            perPage: apiPagination.perPage,
            // Add compatibility fields
            page: apiPagination.currentPage,
            pageSize: apiPagination.perPage,
            hasNext: apiPagination.currentPage < apiPagination.totalPages,
            hasPrev: apiPagination.currentPage > 1,
        };
    }

    async getProperties(
        page: number = 1,
        pageSize: number = 10,
        status?: string,
        propertyTypeId?: string,
        locationId?: string,
        listingType?: string,
        search?: string
    ): Promise<PropertiesResponse> {
        try {
            const params = new URLSearchParams({
                page: page.toString(),
                pageSize: pageSize.toString(),
            });

            if (status) params.append('status', status);
            if (propertyTypeId) params.append('propertyTypeId', propertyTypeId);
            if (locationId) params.append('locationId', locationId);
            if (listingType) params.append('listingType', listingType);
            if (search) params.append('search', search);

            const response = await fetch(`${PROPERTIES_API}?${params.toString()}`, {
                method: 'GET',
                headers: this.getAuthHeaders(),
                credentials: 'include',
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const apiData: ApiPropertiesResponse = await response.json();

            // Transform the response
            const transformedProperties = apiData.data.properties.map(this.transformProperty);
            const transformedPagination = this.transformPagination(apiData.data.pagination);

            const result: PropertiesResponse = {
                status: apiData.status,
                success: apiData.success,
                message: apiData.message,
                data: {
                    properties: transformedProperties,
                    pagination: transformedPagination,
                    statusCounts: apiData.data.statusCounts,
                },
            };

            return result;
        } catch (error) {
            console.error('Error fetching properties:', error);
            throw error;
        }
    }

    async deleteProperty(id: number): Promise<{ status: number; success: boolean; message: string }> {
        try {
            const response = await fetch(PROPERTY_DELETE_API(id), {
                method: 'DELETE',
                headers: this.getAuthHeaders(),
                credentials: 'include',
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Error deleting property:', error);
            throw error;
        }
    }

    async bulkDeleteProperties(ids: number[]): Promise<{ status: number; success: boolean; message: string }> {
        try {
            const response = await fetch(PROPERTY_BULK_DELETE_API, {
                method: 'DELETE',
                headers: {
                    ...this.getAuthHeaders(),
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify({ ids }),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Error bulk deleting properties:', error);
            throw error;
        }
    }

    async bulkUpdatePropertyStatus(ids: number[], status: string, reason?: string): Promise<{ status: number; success: boolean; message: string }> {
        try {
            const response = await fetch(PROPERTY_BULK_STATUS_API, {
                method: 'PUT',
                headers: {
                    ...this.getAuthHeaders(),
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify({ ids, status, reason }),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Error bulk updating property status:', error);
            throw error;
        }
    }

    async addNote(id: number, note: string): Promise<{ status: number; success: boolean; message: string; data?: any }> {
        try {
            const formData = new FormData();
            formData.append('note', note);

            const response = await fetch(PROPERTY_ADD_NOTE_API(id), {
                method: 'POST',
                credentials: 'include',
                body: formData,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Error adding note to property:', error);
            throw error;
        }
    }

    async getPropertyFilters(): Promise<PropertyFiltersResponse> {
        try {
            const response = await fetch(PROPERTIES_FILTERS_API, {
                method: 'GET',
                credentials: 'include',
                headers: this.getAuthHeaders(),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: PropertyFiltersResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error fetching property filters:', error);
            throw error;
        }
    }

    async getStats(): Promise<PropertyStatsResponse> {
        try {
            console.log('Making request to:', PROPERTIES_STATS_API);
            const response = await fetch(PROPERTIES_STATS_API, {
                method: 'GET',
                headers: this.getAuthHeaders(),
                credentials: 'include',
            });

            console.log('Response status:', response.status);
            console.log('Response ok:', response.ok);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: PropertyStatsResponse = await response.json();
            console.log('Raw API response data:', data);
            return data;
        } catch (error) {
            console.error('Error fetching property stats:', error);
            throw error;
        }
    }

    async approveProperty(id: number): Promise<ApiResponse> {
        try {
            const formData = new FormData();
            formData.append('status', 'available');

            const response = await fetch(PROPERTY_UPDATE_STATUS_API(id), {
                method: 'PUT',
                credentials: 'include',
                body: formData,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error approving property:', error);
            throw error;
        }
    }

    async blockProperty(id: number, reason: string): Promise<ApiResponse> {
        try {
            const formData = new FormData();
            formData.append('status', 'blocked');
            formData.append('reason', reason);

            const response = await fetch(PROPERTY_UPDATE_STATUS_API(id), {
                method: 'PUT',
                credentials: 'include',
                body: formData,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error blocking property:', error);
            throw error;
        }
    }

    async unpublishProperty(id: number): Promise<ApiResponse> {
        try {
            const formData = new FormData();
            formData.append('status', 'unpublished');

            const response = await fetch(PROPERTY_UPDATE_STATUS_API(id), {
                method: 'PUT',
                credentials: 'include',
                body: formData,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error unpublishing property:', error);
            throw error;
        }
    }

    async hideProperty(id: number, reason: string): Promise<ApiResponse> {
        try {
            const formData = new FormData();
            formData.append('reason', reason);

            const response = await fetch(PROPERTY_HIDE_API(id), {
                method: 'PATCH',
                credentials: 'include',
                body: formData,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error hiding property:', error);
            throw error;
        }
    }

    async restoreProperty(id: number): Promise<ApiResponse> {
        try {
            const formData = new FormData();

            const response = await fetch(PROPERTY_RESTORE_API(id), {
                method: 'PATCH',
                credentials: 'include',
                body: formData,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error restoring property:', error);
            throw error;
        }
    }

    async featureProperty(id: number): Promise<ApiResponse> {
        try {
            const response = await fetch(PROPERTY_FEATURE_API(id), {
                method: 'PATCH',
                headers: this.getAuthHeaders(),
                credentials: 'include',
                body: JSON.stringify({ featured: true }),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error featuring property:', error);
            throw error;
        }
    }

    async unfeatureProperty(id: number): Promise<ApiResponse> {
        try {
            const response = await fetch(PROPERTY_FEATURE_API(id), {
                method: 'PATCH',
                headers: this.getAuthHeaders(),
                credentials: 'include',
                body: JSON.stringify({ featured: false }),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error unfeaturing property:', error);
            throw error;
        }
    }

    async verifyProperty(id: number): Promise<ApiResponse> {
        try {
            const response = await fetch(PROPERTY_VERIFY_API(id), {
                method: 'PATCH',
                headers: this.getAuthHeaders(),
                credentials: 'include',
                body: JSON.stringify({ verified: true }),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error verifying property:', error);
            throw error;
        }
    }

    async unverifyProperty(id: number): Promise<ApiResponse> {
        try {
            const response = await fetch(PROPERTY_VERIFY_API(id), {
                method: 'PATCH',
                headers: this.getAuthHeaders(),
                credentials: 'include',
                body: JSON.stringify({ verified: false }),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error unverifying property:', error);
            throw error;
        }
    }
}

// Refactor updatePropertyStatus to be standalone
export async function updatePropertyStatus(propertyId: string, statusValue: string, reason?: string): Promise<{ success: boolean }> {
    try {
        console.log('updatePropertyStatus called with:', { propertyId, statusValue, reason });
        // Use FormData to match the API specification
        const formData = new FormData();
        formData.append('status', statusValue);

        // Add reason if provided (required for blocking)
        if (reason) {
            formData.append('reason', reason);
        }

        console.log('Sending FormData:', { statusValue, reason });



        const response = await fetch(`${PROPERTIES_API}/${propertyId}/status`, {
            method: 'PUT',
            credentials: 'include',
            body: formData,
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Failed to update property status:', errorText);
            return { success: false };
        }

        return { success: true };
    } catch (error) {
        console.error('Error updating property status:', error);
        return { success: false };
    }
}

export async function bulkUpdatePropertyStatus(ids: number[], status: string, reason?: string): Promise<{ status: number; success: boolean; message: string }> {
    try {
        const response = await fetch(PROPERTY_BULK_STATUS_API, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify({ ids, status, reason }),
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error bulk updating property status:', error);
        throw error;
    }
}

export default new PropertyService();
export type { Property, PropertiesResponse, ApiResponse, PropertyStats, PropertyStatsResponse, PropertyNote, PropertyHistoryEntry };
